import { requestClient } from '#/api/request';

import { dictPath } from '../path';

// 根据字典类型集合字典子项Map
export async function getDictListCacheMap(
  dictcode: string, // dictcode多个以英文逗号隔开
) {
  return requestClient.get(
    `${dictPath}/dict/manage/getDictListCacheMap?dictCode=${dictcode}`,
  );
}

/** 根据字典类型获取字典子项列表*/
export async function getDictItemList(dictcode: string) {
  return requestClient.get(
    `${dictPath}/dict/manage/getDictItemList/${dictcode}`,
  );
}
