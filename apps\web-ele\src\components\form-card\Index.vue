<script lang="ts" setup>
import { defineProps } from 'vue';

defineProps({
  title: {
    default: '标题',
    type: String,
  },
});
</script>
<template>
  <ElCard
    class="m-2 !border-0"
    header-class="bg-primary-50 text-primary !py-2 px-4 !rounded !border-0"
    shadow="never"
  >
    <template #header>
      <div class="flex h-8 items-center justify-between">
        <slot name="title">
          <span>{{ title }}</span>
        </slot>
        <slot name="titleMore"></slot>
      </div>
    </template>
    <template #default>
      <div>
        <slot name="default"></slot>
      </div>
    </template>
    <template #footer>
      <div>
        <slot name="footer"></slot>
      </div>
    </template>
  </ElCard>
</template>
