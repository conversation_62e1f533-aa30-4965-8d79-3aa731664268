<script lang="ts" setup>
import { defineProps } from 'vue';

defineProps({
  number: {
    type: Number,
    required: true,
  },
  title: {
    type: String,
    default: '卡片标题',
  },
  content: {
    type: String,
    default: '卡片内容区域，可自定义插槽内容...',
  },
});
</script>

<template>
  <div
    class="relative w-full overflow-hidden rounded-lg border-2 border-yellow-500 bg-white shadow-sm transition-all duration-300 hover:shadow-md"
  >
    <!-- 不旋转数字的三角形角标 -->
    <div
      class="clip-triangle absolute left-0 top-0 z-10 flex h-12 w-12 items-center justify-center bg-yellow-500 p-0"
    >
      <span class="absolute left-2 top-1 text-lg text-white">
        {{ number }}
      </span>
    </div>

    <!-- 卡片内容区域 -->
    <div class="relative p-5 pt-6">
      <slot name="header">
        <h3 class="mb-2 text-lg font-semibold text-gray-800">{{ title }}</h3>
      </slot>

      <slot name="content">
        <p class="mb-4 text-gray-600">{{ content }}</p>
      </slot>

      <slot name="footer">
        <div class="mt-4 flex justify-end space-x-3">
          <slot name="bottom-right"></slot>
        </div>
      </slot>
    </div>
  </div>
</template>

<style scoped>
.clip-triangle {
  clip-path: polygon(0 0, 0% 100%, 100% 0);
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;

  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}

.border-yellow-500 {
  --tw-border-opacity: 1;

  border-color: rgb(249 115 22 / var(--tw-bg-opacity));
}
</style>
