import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 1000,
      title: $t('demos.title'),
    },
    name: 'Demos',
    path: '/demos',
    children: [
      {
        meta: {
          title: $t('demos.elementPlus'),
        },
        name: 'NaiveDemos',
        path: '/demos/element',
        component: () => import('#/views/demos/element/index.vue'),
      },
      {
        meta: {
          title: $t('demos.form'),
        },
        name: 'BasicForm',
        path: '/demos/form',
        component: () => import('#/views/demos/form/basic.vue'),
      },
      {
        meta: {
          title: '上传组件测试',
        },
        name: 'UploadFiles',
        path: '/demos/upload-files',
        component: () => import('#/views/demos/uploadFiles/index.vue'),
      },
      {
        meta: {
          title: '图片预览测试',
        },
        name: 'ImagePreview',
        path: '/demos/image-preview',
        component: () => import('#/views/demos/imageView/index.vue'),
      },
      {
        meta: {
          title: '文件预览测试',
        },
        name: 'FileViewer',
        path: '/demos/file-viewer',
        component: () => import('#/views/demos/fileViewer/index.vue'),
      },
      {
        meta: {
          title: '图标组件测试',
        },
        name: 'Icon',
        path: '/demos/icon',
        component: () => import('#/views/demos/iconView/index.vue'),
      },
      {
        meta: {
          title: '备用页面',
        },
        name: 'Cs',
        path: '/demos/cs',
        component: () => import('#/views/demos/cs/index.vue'),
      },
      {
        meta: {
          title: '树预览测试',
        },
        name: 'Tree',
        path: '/demos/tree',
        component: () => import('#/views/demos/tree/index.vue'),
      },
      {
        meta: {
          title: '上传图片',
        },
        name: 'UploadPic',
        path: '/demos/upload-pic',
        component: () => import('#/views/demos/uploadPic/index.vue'),
      },
      {
        meta: {
          title: '动态表格',
        },
        name: 'dynamicTable',
        path: '/demos/dynamic-table',
        component: () => import('#/views/demos/dynamicTable/index.vue'),
      },
      {
        meta: {
          title: '动态表单',
        },
        name: 'dynamicForm',
        path: '/demos/dynamic-form',
        component: () => import('#/views/demos/dynamicTable/Form.vue'),
      },
      {
        meta: {
          title: '远程下拉筛选',
        },
        name: 'remoteSearchSelect',
        path: '/demos/remote-search-select',
        component: () =>
          import('#/views/demos/remote-search-select/RemoteSearchSelect.vue'),
      },
      {
        meta: {
          title: '数据和金额处理',
        },
        name: 'decimal',
        path: '/demos/decimal',
        component: () => import('#/views/demos/decimal/demo.vue'),
      },
    ],
  },
];

export default routes;
