<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { CustomSnowflake } from '@girant/utils';
import { ElCard } from 'element-plus';

/** 雪花码 */
const code = ref('');
const newSnow = () => {
  const customSnowflake = new CustomSnowflake();
  code.value = customSnowflake.nextId();
};
</script>
<template>
  <Page description="备用页面" title="备用页面">
    <ElCard header="备用页面">
      <ElButton @click="newSnow">生成雪花码</ElButton>
      当前雪花码{{ code }}
    </ElCard>
  </Page>
</template>
<style></style>
