<script lang="ts" setup>
import type { VbenFormProps } from '@girant/adapter';

import { h, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { DynamicForm } from '@girant-web/dynamic-table-component';
import { useDebounceFn } from '@vueuse/core';
import { ElLoading } from 'element-plus';

import { getUserPage } from '#/api';
import FormCard from '#/components/form-card/Index.vue';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

const fetchUsers = async ({
  keyword,
  pageNum,
  pageSize,
}: {
  keyword: string;
  pageNum: number;
  pageSize: number;
}) => {
  return await getUserPage({ nickname: keyword, pageNum, pageSize });
};

const dynamicFormRef = ref();

const keyword = ref('');
const fetching = ref(false);
// 模拟远程获取数据
function fetchRemoteOptions({ keyword = '选项' }: Record<string, any>) {
  fetching.value = true;
  return new Promise((resolve) => {
    setTimeout(() => {
      const options = Array.from({ length: 10 }).map((_, index) => ({
        label: `${keyword}-${index}`,
        value: `${keyword}-${index}`,
      }));
      resolve(options);
      fetching.value = false;
    }, 1000);
  });
}
const formOptions = ref<VbenFormProps>({
  schema: [
    {
      component: 'Input',
      fieldName: 'd1',
      label: 'String',
    },
    {
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: () => {
        return {
          api: fetchRemoteOptions,
          // 禁止本地过滤
          filterable: true,
          // 如果正在获取数据，使用插槽显示一个loading
          notFoundContent: fetching.value ? undefined : null,
          // 搜索词变化时记录下来， 使用useDebounceFn防抖。
          onSearch: useDebounceFn((value: string) => {
            keyword.value = value;
          }, 300),
          // 远程搜索参数。当搜索词变化时，params也会更新
          params: {
            keyword: keyword.value || undefined,
          },
          showSearch: true,
        };
      },
      // 字段名
      fieldName: 'd2',
      // 界面显示的label
      label: '远程搜索',
      renderComponentContent: () => {
        return {
          notFoundContent: fetching.value ? h(ElLoading) : undefined,
        };
      },
      rules: 'selectRequired',
    },
    {
      component: h(RemoteSearchSelect, {
        fetchMethod: fetchUsers,
        valueKey: 'userId',
        labelKey: 'nickname',
      }),
      modelPropName: 'modelValue',
      fieldName: 'field2',
      label: '自定义组件',
      rules: 'required',
    },
  ],
});

// 回显值
const data = [
  {
    d1: '2121',
    d2: '选项-0',
    field2: { userId: '1924651351750705154', nickname: '中午了我能' },
  },
  {
    d1: '545',
    d2: '选项-0',
    field2: { userId: '1922089390373982210', nickname: '测试员工1' },
  },
];

// 校验
const validateAllForms = async () => {
  if (dynamicFormRef.value) {
    await dynamicFormRef.value.validateAllForms();
  }
};

const validateForms = async () => {
  if (dynamicFormRef.value) {
    await dynamicFormRef.value.validateAllForms(false);
  }
};

// 获取值
const getAllFormValues = async () => {
  if (dynamicFormRef.value) {
    const data = await dynamicFormRef.value.getAllFormValues();
    console.error(data);
  }
};

const initForm = (params: [any, any]) => {
  const [Form, formApi] = params;
  console.error('新增', [Form, formApi]);
};
const removeForm = (values: any) => {
  // value为现有表单数据
  console.error('删除', values);
};

const removeAllForms = (values: any) => {
  // value为现有表单数据
  console.error('全部删除', values);
};
</script>

<template>
  <Page>
    <FormCard title="动态表单">
      <template #default>
        <div class="button-group">
          <ElButton @click="validateAllForms">表单校验</ElButton>
          <ElButton @click="validateForms">表单校验(无提示)</ElButton>
          <ElButton @click="getAllFormValues">获取值</ElButton>
        </div>
        <DynamicForm
          ref="dynamicFormRef"
          @init-form="initForm"
          @remove-form="removeForm"
          @remove-all-forms="removeAllForms"
          :form-data="data"
          :form-options="formOptions"
        />
      </template>
      <template #footer>
        <span>卡片底部内容</span>
      </template>
    </FormCard>
  </Page>
</template>
