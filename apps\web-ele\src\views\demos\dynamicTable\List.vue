<script setup lang="ts">
// 从 API 模块导入 SystemRole 类型
import type { SystemRoleApi } from '#/api'; // 根据实际路径调整

import { onMounted, ref } from 'vue';

import { getRoleList } from '#/api';

const emit = defineEmits(['checkedData']);

const searchValue = ref('');

interface ListItem {
  value: string;
  label: string;
}

const options = ref<ListItem[]>([]);

const fetchRoles = async () => {
  try {
    const params = { pageSize: 10, pageNum: 1, roleName: searchValue.value };
    const result: SystemRoleApi.SystemRole[] = await getRoleList(params);
    options.value =
      result?.records?.map((item: SystemRoleApi.SystemRole) => ({
        value: item.roleId,
        label: item.roleName,
      })) || [];
  } catch (error) {
    console.error('加载选项失败:', error);
    return [];
  }
};

const checkedData = (obj: any) => {
  emit('checkedData', obj);
};

onMounted(async () => {
  await fetchRoles();
});
</script>

<template>
  <ElInput
    v-model="searchValue"
    @input="fetchRoles"
    placeholder="关键字检索查询更多数据"
  />
  <div
    class="m-2 cursor-pointer rounded-sm p-2 hover:bg-orange-400 hover:text-white"
    v-for="o in options"
    :key="o.value"
    @click="checkedData(o)"
  >
    {{ o.label }}
  </div>
</template>
