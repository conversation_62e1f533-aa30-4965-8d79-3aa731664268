<script setup lang="ts">
/**
 * @demos fileViewer
 * @description 文件预览组件测试
 * <AUTHOR>
 * @date [2025-03-18]
 */

import { Page } from '@vben/common-ui';

import { fileViewer } from '@girant-web/file-view-component';
// import { fileViewer } from './es';
// import { fileViewer } from '../../../../../../girant/components/file-view/src/index.ts';
</script>
<template>
  <Page description="文件预览组件使用测试" title="文件预览组件使用测试">
    <fileViewer
      file-id="1906599391108050945"
      file-type="pdf"
      height="100px"
      width="100px"
    />
    <fileViewer file-id="1906599391087079425" file-type="docx" />
    <fileViewer file-id="1944604298565980162" file-type="xlsx" />
    <fileViewer file-id="1901809589200883713" file-type="txt" />
  </Page>
</template>
<style></style>
