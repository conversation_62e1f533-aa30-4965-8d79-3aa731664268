<script setup lang="ts">
import { Page } from '@vben/common-ui';
import { SvgAvatar1Icon } from '@vben/icons';

import { IconView } from '@girant-web/icon-view-component';
import { ElCard } from 'element-plus';
</script>
<template>
  <Page description="图标组件使用测试" title="图标组件使用测试">
    <ElCard header="传入 Tailwind CSS 类名设置图标">
      <IconView icon="icon-[mdi--folder]" />
    </ElCard>

    <ElCard header="传入 Svg 设置图标">
      <IconView :icon="SvgAvatar1Icon" />
    </ElCard>
  </Page>
</template>
<style></style>
