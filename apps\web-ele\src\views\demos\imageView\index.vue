<script setup lang="ts">
/**
 * @demos ImageViewer
 * @description 图片预览组件测试
 * <AUTHOR>
 * @date [2025-03-18]
 */

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

// import { ImageViewer } from '@girant-web/img-view-component';
import { ElCard } from 'element-plus';

// 本地组件
import { ImageViewer } from '../../../../../../girant/components/img-view/src';

const imgUrl = ref('/static/watermark.png');
</script>
<template>
  <Page description="图片预览组件使用测试" title="图片预览组件使用测试">
    <ElCard class="m-3" header="本地图片 单个图片">
      <el-button
        @click="
          () => {
            imgUrl === '/static/logo.png'
              ? (imgUrl = '/static/watermark.png')
              : (imgUrl = '/static/logo.png');
          }
        "
      >
        改变图片地址
      </el-button>
      <ImageViewer :img-url="imgUrl" img-css="w-[150px] h-[150px]" />
    </ElCard>
    <ElCard class="m-3" header="本地图片 多个图片">
      <ImageViewer
        :img-url="[
          '/static/watermark.png',
          '/static/logo.png',
          '/static/logo.png',
          '/static/logo.png',
          '/static/logo.png',
          '/static/logo.png',
          '/static/logo.png',
          '/static/logo.png',
          '/static/logo.png',
          '/static/logo.png',
        ]"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
    <ElCard class="m-3" header="图片id 单个图片">
      <ImageViewer img-id="1943607951515783170" img-css="w-[150px] h-[150px]" />
    </ElCard>
    <ElCard class="m-3" header="图片id 多个图片">
      <ImageViewer
        :img-id="['1943607951515783170', '1940719421792645122']"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
    <ElCard class="m-3" header="流水号 多个图片">
      <ImageViewer
        serial-number="579831102911284224"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
    <ElCard class="m-3" header="混合使用 多个图片">
      <ImageViewer
        img-url="/static/watermark.png"
        :img-id="['1943607951515783170', '1940719421792645122']"
        serial-number="579831102911284224"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
  </Page>
</template>
