<script setup lang="ts">
import type {
  fileListType,
  TableColumn,
  tableLabelType,
} from '@girant-web/upload-files-component';
/**
 * @demos UploadFiles
 * @description 文件上传组件测试
 * <AUTHOR>
 * @date [2025-03-18]
 */
import type { UploadRequestOptions } from 'element-plus';

import { h, ref } from 'vue';

import { EllipsisText, Page } from '@vben/common-ui';

import { UploadFiles } from '@girant-web/upload-files-component';
import { CustomSnowflake } from '@girant/utils';
import { ElButton, ElCard } from 'element-plus';

import { requestClient } from '#/api/request';

// 文件列表
const defaultFileList: fileListType[] = [
  {
    name: 'cfc45705-4bc4-404d-b867-a9cfcca79f9d.xlsx',
    fileId: '1906599392202764290',
    originalName: '导入客户模板.xlsx',
    serialNumber: '561803399113212928',
    fileSize: 99,
    isImg: false,
    status: 'success',
  },
  {
    name: 'e85b5a72-0e3f-4b1b-ad93-03a183e37db2.docx',
    fileId: '1906599391087079425',
    originalName: '测试用.docx',
    serialNumber: '561803399113212928',
    fileSize: 9,
    isImg: false,
    status: 'success',
  },
  {
    name: '59caded2-7446-44c2-9ff2-071110694a9d.pdf',
    fileId: '1906599391108050945',
    originalName: '测试用.pdf',
    serialNumber: '561803399113212928',
    fileSize: 27,
    isImg: false,
    status: 'success',
  },
  {
    name: '89c53a5e-871e-4f62-a9d0-a246cd7ed1bf.txt',
    fileId: '1901809589200883713',
    originalName: '1111.txt',
    serialNumber: '557013935178384384',
    fileSize: 1,
    isImg: false,
    status: 'success',
  },
];

// 接口地址
const defaultFileList2 = [
  '/file-manage/view/v1/file/getFile/1906597646596354050',
  '/file-manage/view/v1/file/getFile/1906597983990362113',
  '/file-manage/view/v1/file/getFile/1906597959919251457',
];

// 要生成的雪花码
const serialCode = ref();
const customSnowflake = new CustomSnowflake();
serialCode.value = customSnowflake.nextId();

// 获取子组件实例
const childRef = ref();
const A1Ref = ref();
// 自定义上传函数
const customUpload = async (options: UploadRequestOptions) => {
  const fileList = childRef.value!.fileList;
  // 找到对应的列表文件
  const currentFile = fileList.find(
    (file: any) => file.uid === options.file.uid,
  );
  if (currentFile) {
    currentFile.status = 'uploading'; // 文件状态设置为上传中
  } else return; // 找不到文件直接退出
  // 要发送的参数
  const data = {
    file: options.file,
    serialNumber: serialCode.value,
  };

  // 调用上传接口 并返回结果给上传组件
  return await requestClient.upload<UploadRequestOptions>(
    '/file-manage/view/v1/file/upload',
    data,
    {
      // 上传进度钩子
      onUploadProgress: (progressEvent) => {
        // 计算上传进度
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total!,
        );
        // 更新进度
        currentFile.percentage = percentCompleted;
      },
    },
  );
};
// 表格列配置渲染 根据是否是编辑模式进行不同的渲染
const tableColumn: TableColumn[] = [
  {
    label: () => '测试自定义列表子项',
    render: (scope) => {
      return h(
        EllipsisText,
        {},
        {
          default: () => {
            return scope.row.originalName || scope.row.name;
          },
        },
      );
    },
  },
];
// 自定义列表头
const tableLabel: tableLabelType = {
  fileName: '自定义文件名',
  fileSize: '自定义文件大小',
  fileStatus: '自定义文件状态',
  filePreview: '自定义文件预览',
  fileOperate: '自定义操作',
  fileProgress: '自定义进度条',
};
// 上传文件
const uploadFile = async () => {
  A1Ref.value?.fileList.forEach((file: any) => {
    if (file.status === 'fail') {
      file.status = 'ready';
    }
  });
  A1Ref.value?.submitUpload();
};
const isButton = ref(false);
const serialNumber = ref('1906597959894085632');
</script>
<template>
  <Page description="上传组件使用测试" title="上传组件使用测试">
    <ElCard header="添加模式 关闭表格 开启文件缩略图" body-class="w-[400px]">
      <ElButton @click="uploadFile">上传</ElButton>
      <ElButton @click="isButton = !isButton">操作按钮区域显示切换</ElButton>
      <UploadFiles
        mode="addMode"
        ref="A1Ref"
        :show-operat-button="isButton"
        :show-table="false"
        :show-thumbnail="true"
      >
        <template #trigger>
          <i class="icon-[bx--folder] h-[50px] w-[50px]"></i>
          <div>点击或将文件拖拽到这里上传</div>
          <div>支持扩展名:rar .zip .doc .docx .pdf .jpg...</div>
        </template>
      </UploadFiles>
    </ElCard>
    <!-- 使用模式默认配置 -->
    <ElCard header="添加模式 控制列显示">
      <UploadFiles
        mode="addMode"
        :stripe="true"
        :border="true"
        :clear-files="true"
        :table-label="{
          fileName: '自定义文件名',
          filePreview: '自定义文件预览',
          fileOperate: '自定义操作',
        }"
      />
    </ElCard>

    <ElCard header="编辑模式 自动上传">
      <UploadFiles mode="editMode" :auto-upload="true" />
    </ElCard>

    <ElCard header="添加模式 关闭拖拽">
      <UploadFiles mode="addMode" :drag="false" />
    </ElCard>

    <ElCard header="添加模式 禁用多选">
      <UploadFiles mode="addMode" :multiple="false" />
    </ElCard>

    <ElCard header="添加模式 限制文件大小">
      <UploadFiles mode="addMode" :file-size="0.1" />
    </ElCard>
    <ElCard header="添加模式 限制文件格式 ['image/jpeg', 'image/png']">
      <UploadFiles mode="addMode" :list-type="['image/jpeg', 'image/png']" />
    </ElCard>
    <ElCard header="添加模式 立即上传">
      <UploadFiles mode="addMode" :auto-upload="true" />
    </ElCard>

    <ElCard header="添加模式 自定义上传函数">
      <UploadFiles
        mode="addMode"
        :custom-upload="customUpload"
        ref="childRef"
        :auto-upload="true"
      />
    </ElCard>

    <ElCard header="编辑模式 文件列表回显 关闭删除">
      <UploadFiles
        mode="editMode"
        :default-file-list="defaultFileList"
        :delete-file="false"
      />
    </ElCard>

    <ElCard header="编辑模式 文件列表回显">
      <UploadFiles mode="editMode" :default-file-list="defaultFileList" />
    </ElCard>

    <ElCard header="编辑模式 接口地址回显">
      <UploadFiles mode="editMode" :default-file-url="defaultFileList2" />
    </ElCard>

    <ElCard header="编辑模式 根据雪花码回显">
      <ElButton @click="serialNumber = '595535907403072512'">
        改变雪花码1
      </ElButton>
      <ElButton @click="serialNumber = '595536507607974912'">
        改变雪花码2
      </ElButton>
      <UploadFiles
        mode="editMode"
        :serial-number="serialNumber"
        max-height="400"
      />
    </ElCard>

    <ElCard header="编辑模式 使用trigger插槽自定义上传区域">
      <UploadFiles mode="editMode" :default-file-list="defaultFileList">
        <template #trigger>
          <ElButton type="primary">自定义上传区域</ElButton>
        </template>
      </UploadFiles>
    </ElCard>

    <ElCard header="编辑模式 自定义列表头">
      <UploadFiles
        mode="editMode"
        :table-label="tableLabel"
        :default-file-url="defaultFileList2"
      />
    </ElCard>

    <ElCard header="编辑模式 自定义列表子项">
      <UploadFiles
        mode="editMode"
        :table-column="tableColumn"
        :default-file-url="defaultFileList2"
      />
    </ElCard>

    <ElCard header="只读模式 根据接口地址回显示">
      <UploadFiles mode="readMode" :default-file-url="defaultFileList2" />
    </ElCard>
  </Page>
</template>
