<!-- eslint-disable no-console -->
<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { UploadPic } from '@girant-web/upload-pic-component';

/**
 * @function handleSuccess
 * @description 上传成功钩子
 */
const handleSuccess = (res: any) => {
  console.log('上传成功', res);
};
const handleSuccess2 = async (response: any) => {
  console.log('上传成功', response);
  // 等待下一个 tick，确保 v-model 更新完成
  // await nextTick();
  console.log('图片id', imgSrc.value);
};
const uploadRef = ref();
const uploadRef2 = ref();
const imgSrc = ref('1946019459742896129');
const changeImg = () => {
  console.log('切换图片id', imgSrc.value);
  imgSrc.value === '1946019459742896129'
    ? (imgSrc.value = '1945802694446354434')
    : (imgSrc.value = '1946019459742896129');

  console.log('图片id', imgSrc.value);
};
</script>
<template>
  <Page description="上传头像" title="上传头像">
    <ElCard header="上传头像 流水号回显">
      <UploadPic
        @img-upload-success="handleSuccess"
        ref="uploadRef"
        :auto-upload="false"
        serial-number="601007223673455616"
        :limit="3"
      />
      <ElButton @click="uploadRef?.uploadAllImg">上传</ElButton>
      <ElButton
        @click="
          () => {
            console.log(uploadRef?.getAllImgList());
          }
        "
      >
        获取全部图片
      </ElButton>
      <ElButton
        @click="
          () => {
            console.log(uploadRef?.getSerialNumber());
          }
        "
      >
        获取流水号
      </ElButton>
    </ElCard>
    <ElCard header="上传头像  图片id回显">
      <UploadPic
        @img-upload-success="handleSuccess2"
        ref="uploadRef2"
        v-model:img-id="imgSrc"
        :limit="3"
      />
      <ElButton @click="changeImg()">切换图片id</ElButton>
      <ElButton @click="uploadRef2?.uploadAllImg">上传</ElButton>
      <ElButton
        @click="
          () => {
            console.log(uploadRef2?.getAllImgList());
          }
        "
      >
        获取全部图片
      </ElButton>
      <ElButton
        @click="
          () => {
            console.log(uploadRef2?.getImgId());
          }
        "
      >
        获取图片id
      </ElButton>
    </ElCard>
  </Page>
</template>
