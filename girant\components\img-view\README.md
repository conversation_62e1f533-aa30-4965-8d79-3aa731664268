# 图片预览组件

组件名：ImageViewer

导入使用 import { ImageViewer } from '@girant-web/img-view-component';

使用了私有仓库的：@girant/utils

使用了Vben的 @vben/request element-plus @vben/stores @vben/preferences

更多API参考[Image 图片 | Element Plus](https://element-plus.org/zh-CN/component/image.html#image-api)

### props

获取图片优先级 imgId>imgUrl>serialNumber。

会自动判断传入的imgId、serialNumber、imgUrl是否有多个图片，如果有多个，则开启多图显示。如果同时传入imgId和serialNumber、imgUrl会将他们的所有图片整合渲染出来。

| 属性名 | 类型 | props默认值 | 说明 |
| :-- | --- | --- | --- |
| previewUrl | String | '/file-manage/view/v1/file/preview/' | 获取图片请求地址前缀 |
| getImgListUrl | String | '/file-manage/view/v1/file/getFileList' | 根据流水号获取图片列表地址 |
| imgUrl | String \| Array | null | 本地图片地址（用于回显图片） |
| imgId | String \| Array | null | 图片id（用于回显图片）(支持传入数组) |
| serialNumber | String | '' | 流水号19位（用于获取图片列表） |
| noImgCss | String | 'h-full w-full flex items-center justify-center text-[30px] text-slate-300' | 图片加载失败提示的css 使用error插槽时无效 |
| isThumb | Boolean | true | 是否获取缩略图 在true的情况下是初始获取缩略图进行展示，只有点击预览后才会获取原图。为false的情况 下，初始获取的是原图，点击预览不会发送请求。 |
| imgCss | String | 'w-[50px]' | 图片的css |
| imgFit | String | ‘scale-down’ | 图片适应容器策略fit 可选择contain、cover、fill、none、scale-down |

### Exposes

| 对象名        | 类型                | 说明                 |
| ------------- | ------------------- | -------------------- |
| loadImage     | () => Promise<void> | 加载图片（重新获取） |
| getAllImgList | () => ImgList       | 获取全部图片列表     |

### Slots

| 插槽名 | 说明             |
| ------ | ---------------- |
| error  | 加载失败占位区域 |

### 例子

#### 1.正常使用

```vue
<ElCard class="m-3" header="本地图片 单个图片">
      <ImageViewer
        img-url="/static/watermark.png"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
<ElCard class="m-3" header="本地图片 多个图片">
      <ImageViewer
        :img-url="['/static/watermark.png','/static/logo.png']"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
<ElCard class="m-3" header="图片id 单个图片">
      <ImageViewer img-id="1943607951515783170" img-css="w-[150px] h-[150px]" />
    </ElCard>
<ElCard class="m-3" header="图片id 多个图片">
      <ImageViewer
        :img-id="['1943607951515783170', '1940719421792645122']"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
<ElCard class="m-3" header="流水号 多个图片">
      <ImageViewer
        serial-number="579831102911284224"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
<ElCard class="m-3" header="混合使用 多个图片">
      <ImageViewer
        img-url="/static/watermark.png"
        :img-id="['1943607951515783170', '1940719421792645122']"
        serial-number="579831102911284224"
        img-css="w-[150px] h-[150px]"
      />
    </ElCard>
```

#### 2.在useVbenForm中使用

```ts
//imgId显示
{
  component: h(ImageViewer, {
    imgCss: 'size-40',
  }),
  modelPropName: 'imgId',
  fieldName: 'avatarId',
  formItemClass: 'col-span-full',
  label: '图片',
},
//serialNumber显示
{
  component: h(ImageViewer, {
    class: 'w-full',
  }),
  modelPropName: 'serialNumber',
  fieldName: 'serialNumber',
  formItemClass: 'col-span-full',
  label: '图片',
},
```

#### 3.在vxeTableGrid中使用

在列表中一般是显示单个图片，比如头像

```ts
//imgId显示
    {
      field: 'avatarId',
      minWidth: 100,
      slots: {
        default: ({ row }) => {
          if (!row.avatarId) {
            return h('div', null, '');
          }
          return h(ImageViewer, {
            imgId: row.avatarId,
            imgCss: 'h-[50px] max-w-[110px] text-[30px] text-slate-300',
          });
        },
      },
      title: '头像',
    }
```
