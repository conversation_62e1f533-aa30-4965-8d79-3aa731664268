# 上传图片组件 UploadPic

使用了vben的 element-plus 、 @vben/request

导入import { UploadPic } from '@girant-web/upload-pic-component';

封装了element-plus的el-upload组件，有些没有写的属性、方法、插槽可以在element-plus中查找

在打包完成后 添加import '../assets/upload-pic.css';到src/vue/index.vue.mjs中,以免css样式丢失

参考[Upload 上传 | Element Plus](https://element-plus.org/zh-CN/component/upload.html#)

组件的v-bind="$attrs"绑定在ElUpload上、<slot></slot>也放在ElUpload中

### Props

当图片数量达到limit设置的上限后，需要删除图片才能继续选择图片

| 属性名 | 类型 | props默认值 | 说明 |
| :-: | :-: | :-: | :-- |
| autoUpload | Boolean | true | 是否自动上传 |
| fileSize | Number | 1 | 图片大小限制 mb |
| limit | Number | 1 | 限制上传图片数量(0为不限制，设置如果大于1，说明开启多图片上传) |
| imgId | String \| Array | null | 回显图片id(优先级高于流水号)(支持传入数组)(使用了v-model:img-id进行双向绑定，绑定时需要一个变量) |
| serialNumber | String | '' | 传入的流水号19位（如果没有传入，则由组件生成）（在上传时会附带，在编辑时根据流水号获取图片列表回显） |
| multiple | Boolean | false | 是否支持多选文件(选择上传图片时) |
| isLoadImgs | Boolean | true | 是否发送请求获取图片（如果填入false则不获取图片回显） |
| showDeleteBtn | Boolean | true | 是否显示删除按钮 |
| urlConfig | Object | urlConfig | 文件请求路径配置。见下表textConfigType。默认值见下表urlConfig |
| textConfig | Object | textConfig | 文本配置。类型说明见下表textConfigType。默认值见下表textConfig |
| imgFit | String | ‘scale-down’ | 显示的图片适应容器策略fit 可选择contain、cover、fill、none、scale-down |
| imgProps | Object | {} | 绑定到图片的Props属性，图片展示用的是ElImage组件 |
| imgWidth | Number | 160 | 图片盒子的宽度 设置了ElUpload组件.el-upload--picture-card和.el-upload-list\_\_item盒子的宽度 |
| imgHeight | Number | 160 | 图片盒子的高度 |

####

### Slots

插槽参考element-plus的upload组件,支持upload组件slot

| 插槽名 | 参考 | 说明 |
| --- | --- | --- |
| trigger | ElUpload的trigger | 触发文件选择框的内容（选择区域） |
| default | ElUpload的default | 自定义默认内容 （trigger下面区域） |
| tip | ElUpload的tip | 提示文字提示说明文字（default下面区域） |
| file | ElUpload的file | 文件缩略图插槽（tip下面区域）。插槽绑定了一个:scope="scope" |

### Exposes

支持upload组件Exposes

| 对象名          | 类型                | 说明                 |
| --------------- | ------------------- | -------------------- |
| uploadAllImg    | Function            | 上传全部图片         |
| getAllImgList   | () => ImgList       | 获取全部图片列表     |
| clearImgList    | () => Promise<void> | 清空图片列表         |
| getSerialNumber | ()=>string          | 获取上传使用的流水号 |
| getImgId        | ()=>string \| Array | 获取图片id           |

### Event

参考Element Plus

| 事件 | 类型 | 说明 |
| --- | --- | --- |
| imgUploadSuccess | (response)=>void | 上传完成后 返回上传结果信息（每一个图片上传成功后都会触发），response的值是后端接口返回的值 |
| imgListDeleteSuccess | ()=>void | 清空图片完成后触发 |
| imgDeleteSuccess | (file)=>void | 删除单个图片完成后触发，file是图片详细信息 |

### 类型说明

```ts
//请求地址
interface urlConfigType{
    uploadFileUrl: string;  // 图片上传接口地址
    previewUrl: string;		// 图片预览地址（回显地址）
    getImgListUrl：string;  // 根据流水号获取图片列表地址（回显地址）
    deleteFileUrl：string; // 删除图片接口地址
}
//文本配置类型
interface textConfigType {
  promptText: string;	//上传区域文字 （trigger插槽）
  tipText: string; 		//tip提示文字 （tip插槽）
}
```

### 组件默认值说明

```ts
//请求地址
const urlConfig={
    uploadFileUrl: '/file-manage/view/v1/file/upload';  	// 图片上传接口地址
    previewUrl: '/file-manage/view/v1/file/preview';		// 图片预览地址
    getImgListUrl：'/file-manage/view/v1/file/getFileList';  // 根据流水号获取图片列表地址
    deleteFileUrl:'/file-manage/view/v1/file/remove'; 		//删除图片接口地址
}
//文本配置
const textConfig= {
  	promptText: '上传图片';	//上传区域文字 （trigger插槽）
  	tipText: '选择一张大小在1mb以内,格式为jpg、png的图片';//tip提示文字 （tip插槽）
}
```

### 使用例子

#### 1.正常使用

```vue
<!--单选-->
<UploadPic @img-Upload-success="handleSuccess"></UploadPic>
```

#### 2.在useVbenForm中使用

```TS
//单选
{
   component: h(UploadPic, {
   		class:'size-40'
   },
   modelPropName:'imgId',//绑定imgId进行回显
   fieldName: 'avatarId',
   label: '图片',
   formItemClass: 'col-span-full',
},
//多选
{
  component: h(UploadPic, {
   	class:'size-40'
    limit：10
  },
  modelPropName:'serialNumber',//绑定serialNumber进行回显
  fieldName: 'imgList',
  label: '图片列表',
  formItemClass: 'col-span-full',
},
```
