{"name": "@girant-web/upload-pic-component", "version": "1.0.9", "description": "图片上传组件", "keywords": [], "license": "ISC", "author": "BianLiangXu", "type": "module", "scripts": {"build": "vite build --config vite.build.config.ts"}, "main": "./src/index", "dependencies": {"@girant/utils": "0.0.2", "@vben/request": "workspace:", "element-plus": "2.9.9", "vue": "catalog:"}, "devDependencies": {"@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "vite": "catalog:", "vite-plugin-dts": "catalog:"}}